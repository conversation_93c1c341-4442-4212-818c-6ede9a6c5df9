import { Workspace<PERSON>eaf, TFile, Notice } from "obsidian";
import type GhostSyncPlugin from "../main";
import type { GhostPost } from "../types";
import { ContentConverter } from "../utils/content-converter";
import { SvelteView } from "../components/SvelteView";
import GhostSyncView from "../components/GhostSyncView.svelte";
import PublishDialog from "../components/PublishDialog.svelte";
import PostBrowser from "../components/PostBrowser.svelte";
import type { SyncStatusData, PublishOptions } from "../components/types";
import { ObsidianGhostAPI } from "../api/ghost-api";
import { SyncStatusService } from "../services/sync-status-service";
import { SmartSyncService } from "../services/smart-sync-service";
import { SyncDecision } from "../types";
import { ObsidianAppAdapter } from "../services/obsidian-app-adapter";
import * as path from "path";

export const VIEW_TYPE_GHOST_SYNC_STATUS = 'ghost-sync-status';

export class SvelteSyncStatusView extends SvelteView {
  private currentFile: TFile | null = null;
  private syncStatus: SyncStatusData = {
    title: 'unknown',
    slug: 'unknown',
    status: 'unknown',
    tags: 'unknown',
    featured: 'unknown',
    feature_image: 'unknown',
    visibility: 'unknown',
    primary_tag: 'unknown',
    created_at: 'unknown',
    updated_at: 'unknown',
    published_at: 'unknown',
    synced_at: 'unknown',
    newsletter: 'unknown',
    email_sent: 'unknown'
  };

  private publishDialog: PublishDialog | null = null;
  private postBrowser: PostBrowser | null = null;
  private syncStatusService: SyncStatusService;
  private smartSyncService: SmartSyncService;
  private appAdapter: ObsidianAppAdapter;

  constructor(leaf: WorkspaceLeaf, plugin: GhostSyncPlugin, syncStatusService?: SyncStatusService) {
    super(leaf, plugin);

    this.appAdapter = new ObsidianAppAdapter(this.app);

    if (syncStatusService) {
      this.syncStatusService = syncStatusService;
    } else {
      // Create default service with Ghost API
      const ghostAPI = new ObsidianGhostAPI(plugin.settings.ghostUrl, plugin.settings.ghostAdminApiKey);
      this.syncStatusService = new SyncStatusService({
        ghostAPI,
        appAdapter: this.appAdapter
      });
    }

    // Initialize SmartSyncService
    const ghostAPI = new ObsidianGhostAPI(plugin.settings.ghostUrl, plugin.settings.ghostAdminApiKey);
    this.smartSyncService = new SmartSyncService({
      ghostAPI,
      readFile: (file: TFile) => this.app.vault.read(file),
      writeFile: (file: TFile, content: string) => this.app.vault.modify(file, content),
      parseMarkdown: (content: string) => {
        const parsed = ContentConverter.parseMarkdown(content);
        return { frontMatter: parsed.frontMatter, content: parsed.markdownContent };
      }
    });
  }

  getViewType() {
    return VIEW_TYPE_GHOST_SYNC_STATUS;
  }

  getDisplayText() {
    return 'Ghost';
  }

  getIcon() {
    return 'sync';
  }

  async onOpen() {
    await super.onOpen();

    // Listen for active file changes
    this.registerEvent(
      this.app.workspace.on('active-leaf-change', () => {
        console.log('Ghost tab: active-leaf-change event fired');
        this.updateCurrentFile();
      })
    );

    // Listen for file open events
    this.registerEvent(
      this.app.workspace.on('file-open', (file) => {
        console.log('Ghost tab: file-open event fired for:', file?.path);
        this.updateCurrentFile();
      })
    );

    // Listen for layout changes (more comprehensive)
    this.registerEvent(
      this.app.workspace.on('layout-change', () => {
        console.log('Ghost tab: layout-change event fired');
        this.updateCurrentFile();
      })
    );

    // Listen for file changes
    this.registerEvent(
      this.app.vault.on('modify', (file) => {
        if (file === this.currentFile) {
          console.log('Ghost tab: file modified:', file.path);
          this.updateSyncStatus();
        }
      })
    );

    // Listen for editor changes (more reliable for file switching)
    this.registerEvent(
      this.app.workspace.on('editor-change', (editor, info) => {
        console.log('Ghost tab: editor-change event fired');
        this.updateCurrentFile();
      })
    );

    console.log('Ghost tab: Initial setup complete, calling updateCurrentFile');
    this.updateCurrentFile();
  }

  protected createComponent(container: HTMLElement) {
    const component = this.createSvelteComponent(
      GhostSyncView,
      container,
      {
        currentFile: this.currentFile,
        syncStatus: this.syncStatus
      }
    );

    // Listen to component events
    component.$on('smartSync', async () => {
      await this.smartSync();
    });

    component.$on('publish', () => {
      this.showPublishDialog();
    });

    component.$on('browsePosts', () => {
      this.showPostBrowser();
    });

    return component;
  }

  private recreateComponent() {
    if (this.component) {
      // Destroy the old component
      this.component.$destroy();
      this.component = null;
    }

    // Create a new component with updated props
    const container = this.contentEl;
    container.empty();
    container.addClass('svelte-view-container');
    this.component = this.createComponent(container);
  }

  private updateCurrentFile() {
    const activeFile = this.app.workspace.getActiveFile();
    console.log('Ghost tab: updateCurrentFile called');
    console.log('Ghost tab: activeFile:', activeFile?.path);
    console.log('Ghost tab: currentFile:', this.currentFile?.path);

    if (activeFile !== this.currentFile) {
      console.log('Ghost tab: File changed, updating view');
      this.currentFile = activeFile;
      this.updateSyncStatus();

      // In Svelte 5, we need to recreate the component with new props
      // since $set is deprecated and bindable props should be used
      this.recreateComponent();
    } else {
      console.log('Ghost tab: No file change detected');
    }
  }

  private async updateSyncStatus() {
    console.log('Ghost tab: updateSyncStatus called');
    if (!this.currentFile) {
      console.log('Ghost tab: No current file, skipping sync status update');
      return;
    }

    console.log('Ghost tab: Updating sync status for file:', this.currentFile.path);

    try {
      // Use the service to calculate sync status
      this.syncStatus = await this.syncStatusService.calculateSyncStatus(this.currentFile);

      // In Svelte 5, recreate component with updated props
      this.recreateComponent();

    } catch (error) {
      console.error('Error updating sync status:', error);
      this.resetSyncStatus();
    }
  }



  private resetSyncStatus() {
    this.syncStatus = {
      title: 'unknown',
      slug: 'unknown',
      status: 'unknown',
      tags: 'unknown',
      featured: 'unknown',
      feature_image: 'unknown',
      visibility: 'unknown',
      primary_tag: 'unknown',
      created_at: 'unknown',
      updated_at: 'unknown',
      published_at: 'unknown',
      synced_at: 'unknown',
      newsletter: 'unknown',
      email_sent: 'unknown'
    };

    this.recreateComponent();
  }

  private async smartSync() {
    if (!this.currentFile) {
      new Notice('No file selected');
      return;
    }

    try {
      // Parse local post
      const localPost = await this.smartSyncService.parseLocalPost(this.currentFile);

      // Get Ghost post if it exists
      const slug = localPost.frontMatter.slug || localPost.frontMatter.Slug;
      if (!slug) {
        new Notice('Post must have a slug to sync');
        return;
      }

      const ghostAPI = new ObsidianGhostAPI(this.plugin.settings.ghostUrl, this.plugin.settings.ghostAdminApiKey);
      const ghostPost = await ghostAPI.getPostBySlug(slug);

      // Analyze what sync action is needed
      const analysis = await this.smartSyncService.analyzeSyncNeeded(localPost, ghostPost);

      // Execute sync based on analysis
      switch (analysis.decision) {
        case SyncDecision.SYNC_TO_GHOST:
          new Notice(`${analysis.reason} - syncing to Ghost...`);
          await this.smartSyncService.syncToGhost(localPost);
          new Notice('Synced to Ghost successfully');
          break;

        case SyncDecision.SYNC_FROM_GHOST:
          new Notice(`${analysis.reason} - syncing from Ghost...`);
          if (ghostPost) {
            await this.smartSyncService.syncFromGhost(this.currentFile, ghostPost);
            new Notice('Synced from Ghost successfully');
          }
          break;

        case SyncDecision.CONFLICT:
          new Notice(`Conflict detected: ${analysis.reason}`);
          await this.handleSyncConflict(analysis);
          break;

        case SyncDecision.NO_SYNC_NEEDED:
          new Notice('No sync needed - everything is up to date');
          break;
      }

      // Refresh sync status
      setTimeout(() => this.updateSyncStatus(), 1000);

    } catch (error) {
      console.error('Smart sync failed:', error);
      new Notice(`Sync failed: ${error.message}`);
    }
  }

  private async handleSyncConflict(analysis: any) {
    // For now, ask user which direction to sync
    const choice = await this.showConflictDialog(analysis);

    if (choice === 'to_ghost') {
      new Notice('Syncing local changes to Ghost...');
      await this.smartSyncService.syncToGhost(analysis.localPost);
      new Notice('Synced to Ghost successfully');
    } else if (choice === 'from_ghost') {
      new Notice('Syncing Ghost changes to local...');
      if (analysis.ghostPost) {
        await this.smartSyncService.syncFromGhost(this.currentFile, analysis.ghostPost);
        new Notice('Synced from Ghost successfully');
      }
    }
  }

  private async showConflictDialog(analysis: any): Promise<'to_ghost' | 'from_ghost' | 'cancel'> {
    return new Promise((resolve) => {
      const modal = document.createElement('div');
      modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
      `;

      const content = document.createElement('div');
      content.style.cssText = `
        background: var(--background-primary);
        padding: 20px;
        border-radius: 8px;
        max-width: 500px;
        text-align: center;
      `;

      content.innerHTML = `
        <h2>Sync Conflict Detected</h2>
        <p>${analysis.reason}</p>
        <p>Both the local file and Ghost post have been modified since the last sync.</p>
        <p>Which version would you like to keep?</p>
        <div style="margin-top: 20px;">
          <button id="use-local" style="margin: 5px;">Use Local (sync to Ghost)</button>
          <button id="use-ghost" style="margin: 5px;">Use Ghost (sync from Ghost)</button>
          <button id="cancel" style="margin: 5px;">Cancel</button>
        </div>
      `;

      modal.appendChild(content);
      document.body.appendChild(modal);

      content.querySelector('#use-local')?.addEventListener('click', () => {
        document.body.removeChild(modal);
        resolve('to_ghost');
      });

      content.querySelector('#use-ghost')?.addEventListener('click', () => {
        document.body.removeChild(modal);
        resolve('from_ghost');
      });

      content.querySelector('#cancel')?.addEventListener('click', () => {
        document.body.removeChild(modal);
        resolve('cancel');
      });
    });
  }

  private showPublishDialog() {
    if (!this.syncStatus.ghostPost) {
      new Notice('No Ghost post to publish');
      return;
    }

    // Create modal container
    const modalContainer = document.body.createDiv();

    this.publishDialog = new PublishDialog({
      target: modalContainer,
      props: {
        ghostPost: this.syncStatus.ghostPost,
        show: true
      },
      context: new Map([
        ['ghost-sync-plugin', { plugin: this.plugin }]
      ])
    });

    this.publishDialog.$on('confirm', async (event) => {
      await this.handlePublish(event.detail);
      this.publishDialog?.$destroy();
      modalContainer.remove();
    });

    this.publishDialog.$on('cancel', () => {
      this.publishDialog?.$destroy();
      modalContainer.remove();
    });
  }

  private showPostBrowser() {
    // Create modal container
    const modalContainer = document.body.createDiv();

    this.postBrowser = new PostBrowser({
      target: modalContainer,
      props: {
        show: true
      },
      context: new Map([
        ['ghost-sync-plugin', { plugin: this.plugin }]
      ])
    });

    this.postBrowser.$on('select', async (event) => {
      await this.handlePostSelection(event.detail);
      this.postBrowser?.$destroy();
      modalContainer.remove();
    });

    this.postBrowser.$on('cancel', () => {
      this.postBrowser?.$destroy();
      modalContainer.remove();
    });
  }

  private async handlePublish(options: PublishOptions) {
    // Implementation for publishing
    new Notice(`Publishing with action: ${options.action}`);
  }

  private async handlePostSelection(post: GhostPost) {
    try {
      new Notice(`Syncing "${post.title}" from Ghost...`);

      const articleContent = ContentConverter.convertGhostPostToArticle(post);
      const filename = post.slug + '.md';
      const filePath = path.posix.join(this.plugin.settings.articlesDir, filename);

      const existingFile = this.app.vault.getAbstractFileByPath(filePath);
      if (existingFile) {
        await this.app.vault.modify(existingFile as any, articleContent);
        new Notice(`Updated "${post.title}" in ${filePath}`);
      } else {
        await this.app.vault.create(filePath, articleContent);
        new Notice(`Created "${post.title}" in ${filePath}`);
      }
    } catch (error) {
      console.error("Error syncing selected post:", error);
      new Notice(`Error syncing post: ${error.message}`);
    }
  }
}
