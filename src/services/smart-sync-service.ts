import type { TFile } from "obsidian";
import type { GhostPost, LocalPost, SyncAnalysis, ArticleFrontMatter } from "../types";
import { SyncDecision } from "../types";
import type { ObsidianGhostAPI } from "../api/ghost-api";
import { ContentConverter } from "../utils/content-converter";

export interface SmartSyncServiceDependencies {
  ghostAPI: ObsidianGhostAPI;
  readFile: (file: TFile) => Promise<string>;
  writeFile: (file: TFile, content: string) => Promise<void>;
  parseMarkdown: (content: string) => { frontMatter: ArticleFrontMatter; content: string };
}

/**
 * Service for intelligent bidirectional sync between Obsidian and Ghost
 * Uses timestamps to determine sync direction and avoid conflicts
 */
export class SmartSyncService {
  private dependencies: SmartSyncServiceDependencies;

  constructor(dependencies: SmartSyncServiceDependencies) {
    this.dependencies = dependencies;
  }

  /**
   * Parse a local file into a LocalPost model
   */
  async parseLocalPost(file: TFile): Promise<LocalPost> {
    const content = await this.dependencies.readFile(file);
    const parsed = this.dependencies.parseMarkdown(content);

    // Extract synced_at and changed_at from frontmatter
    const syncedAt = parsed.frontMatter.synced_at || parsed.frontMatter['Synced At'];
    const changedAt = parsed.frontMatter.changed_at || parsed.frontMatter['Changed At'];

    return {
      frontMatter: parsed.frontMatter,
      content: parsed.content,
      syncedAt: syncedAt,
      changedAt: changedAt,
      fileModifiedAt: file.stat?.mtime || 0,
      filePath: file.path
    };
  }

  /**
   * Analyze what sync action should be taken
   */
  async analyzeSyncNeeded(localPost: LocalPost, ghostPost?: GhostPost): Promise<SyncAnalysis> {
    if (!ghostPost) {
      return {
        decision: SyncDecision.SYNC_TO_GHOST,
        localPost,
        reason: "Post doesn't exist in Ghost yet",
        localChangedTime: localPost.changedAt
      };
    }

    const lastSyncTime = localPost.syncedAt;
    const ghostUpdatedTime = ghostPost.updated_at;
    const localChangedTime = localPost.changedAt;

    // If no sync timestamp, this is first sync - check which is newer
    if (!lastSyncTime) {
      const ghostTime = new Date(ghostUpdatedTime).getTime();
      const localTime = localChangedTime ? new Date(localChangedTime).getTime() : 0;

      if (localTime > ghostTime) {
        return {
          decision: SyncDecision.SYNC_TO_GHOST,
          localPost,
          ghostPost,
          reason: "First sync - local content is newer",
          ghostUpdatedTime,
          localChangedTime
        };
      } else {
        return {
          decision: SyncDecision.SYNC_FROM_GHOST,
          localPost,
          ghostPost,
          reason: "First sync - Ghost post is newer",
          ghostUpdatedTime,
          localChangedTime
        };
      }
    }

    const lastSyncTimeMs = new Date(lastSyncTime).getTime();
    const ghostUpdatedTimeMs = new Date(ghostUpdatedTime).getTime();

    // Check if Ghost has been updated since our last sync
    const ghostChangedSinceSync = ghostUpdatedTimeMs > lastSyncTimeMs;

    // Check if local content has been changed since our last sync
    const localChangedSinceSync = localChangedTime && new Date(localChangedTime).getTime() > lastSyncTimeMs;

    if (ghostChangedSinceSync && localChangedSinceSync) {
      return {
        decision: SyncDecision.CONFLICT,
        localPost,
        ghostPost,
        reason: "Both Ghost and local content have changes since last sync",
        lastSyncTime,
        ghostUpdatedTime,
        localChangedTime
      };
    } else if (ghostChangedSinceSync) {
      return {
        decision: SyncDecision.SYNC_FROM_GHOST,
        localPost,
        ghostPost,
        reason: "Ghost has newer changes",
        lastSyncTime,
        ghostUpdatedTime,
        localChangedTime
      };
    } else if (localChangedSinceSync) {
      return {
        decision: SyncDecision.SYNC_TO_GHOST,
        localPost,
        ghostPost,
        reason: "Local content has newer changes",
        lastSyncTime,
        ghostUpdatedTime,
        localChangedTime
      };
    } else {
      return {
        decision: SyncDecision.NO_SYNC_NEEDED,
        localPost,
        ghostPost,
        reason: "No changes detected since last sync",
        lastSyncTime,
        ghostUpdatedTime,
        localChangedTime
      };
    }
  }

  /**
   * Sync local post to Ghost
   */
  async syncToGhost(localPost: LocalPost): Promise<GhostPost> {
    const slug = localPost.frontMatter.slug || localPost.frontMatter.Slug;
    if (!slug) {
      throw new Error("Post must have a slug to sync to Ghost");
    }

    // Check if post exists in Ghost
    const existingPost = await this.dependencies.ghostAPI.getPostBySlug(slug);

    // Create Ghost post data
    const postData = ContentConverter.createGhostPostData(
      localPost.frontMatter,
      localPost.content,
      {
        isUpdate: !!existingPost,
        existingPost: existingPost
      }
    );

    let result: GhostPost;
    if (existingPost) {
      result = await this.dependencies.ghostAPI.updatePost(postData);
    } else {
      result = await this.dependencies.ghostAPI.createPost(postData);
    }

    // Update local file with sync timestamp
    await this.updateSyncTimestamp(localPost, result.updated_at);

    return result;
  }

  /**
   * Sync from Ghost to local post
   */
  async syncFromGhost(file: TFile, ghostPost: GhostPost): Promise<void> {
    const articleContent = ContentConverter.convertGhostPostToArticle(ghostPost);
    await this.dependencies.writeFile(file, articleContent);

    // The converted content already includes the updated synced_at timestamp
  }

  /**
   * Update the synced_at timestamp in the local file
   */
  private async updateSyncTimestamp(localPost: LocalPost, syncTime: string): Promise<void> {
    const now = new Date().toISOString();

    // Update the frontmatter with sync and change timestamps
    const updatedFrontMatter = {
      ...localPost.frontMatter,
      'Synced At': syncTime,
      synced_at: syncTime,
      'Changed At': now,
      changed_at: now
    };

    // Reconstruct the file content
    const yamlFrontmatter = ContentConverter.objectToYaml(updatedFrontMatter);
    const updatedContent = `---\n${yamlFrontmatter}---\n\n${localPost.content}`;

    // We need the file object to write, but we only have the path
    // This will be handled by the calling code that has access to the TFile
    // For now, we'll update the localPost object
    localPost.frontMatter = updatedFrontMatter;
    localPost.syncedAt = syncTime;
    localPost.changedAt = now;
  }
}
