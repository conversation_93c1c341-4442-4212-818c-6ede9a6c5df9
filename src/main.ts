import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ice, <PERSON>lugin, SuggestModal, TFile, WorkspaceLeaf } from "obsidian";
import * as path from "path";
import { ContentConverter } from "./utils/content-converter";
import { PropertyMapper } from "./utils/property-mapping";
import { ObsidianGhostAPI } from "./api/ghost-api";
import { SvelteSyncStatusView, VIEW_TYPE_GHOST_SYNC_STATUS } from "./views/sync-status-view";
import { GhostSyncSettingTab } from "./settings/settings-tab";
import { GhostSyncSettings, GhostPost, GhostNewsletter } from "./types";

// Post selection modal
class PostSelectionModal extends SuggestModal<GhostPost> {
	private posts: GhostPost[];
	private onSelect: (post: GhostPost) => void;

	constructor(app: App, posts: GhostPost[], onSelect: (post: GhostPost) => void) {
		super(app);
		this.posts = posts;
		this.onSelect = onSelect;
		this.setPlaceholder("Type to search posts...");
	}

	getSuggestions(query: string): GhostPost[] {
		return this.posts.filter(post =>
			post.title.toLowerCase().includes(query.toLowerCase()) ||
			post.slug.toLowerCase().includes(query.toLowerCase())
		);
	}

	renderSuggestion(post: GhostPost, el: HTMLElement) {
		const container = el.createDiv({ cls: "ghost-post-suggestion" });

		const title = container.createDiv({ cls: "ghost-post-title" });
		title.setText(post.title);

		const meta = container.createDiv({ cls: "ghost-post-meta" });
		const status = post.status === 'published' ? '📄' : '📝';
		const publishedDate = post.published_at ?
			new Date(post.published_at).toLocaleDateString() :
			'Draft';
		const tags = post.tags?.map((t: any) => t.name).join(', ') || 'No tags';
		meta.setText(`${status} ${publishedDate} • ${tags}`);
	}

	onChooseSuggestion(post: GhostPost, evt: MouseEvent | KeyboardEvent) {
		this.onSelect(post);
	}
}

const DEFAULT_SETTINGS: GhostSyncSettings = {
	ghostUrl: "https://your-site.ghost.io",
	ghostAdminApiKey: "",
	articlesDir: "articles",
	verbose: false
};

export default class GhostSyncPlugin extends Plugin {
	settings: GhostSyncSettings;
	private newsletters: GhostNewsletter[] = [];
	private newslettersLoaded: boolean = false;

	async onload() {
		await this.loadSettings();

		// Load newsletters in the background
		this.loadNewsletters();

		// Register the sync status view
		this.registerView(
			VIEW_TYPE_GHOST_SYNC_STATUS,
			(leaf) => new SvelteSyncStatusView(leaf, this)
		);

		// Add ribbon icon for syncing current post to Ghost
		this.addRibbonIcon("ghost", "Sync current post to Ghost", () => {
			this.syncCurrentPostToGhost();
		});

		// Add ribbon icon for sync status view
		this.addRibbonIcon("ghost", "Open Ghost Sync Status", () => {
			this.activateSyncStatusView();
		});

		// Add command for syncing current post to Ghost
		this.addCommand({
			id: "sync-current-to-ghost",
			name: "Sync current post to Ghost",
			editorCallback: () => {
				this.syncCurrentPostToGhost();
			}
		});

		// Add command for syncing current file
		this.addCommand({
			id: "sync-current-file",
			name: "Sync current file",
			editorCallback: () => {
				this.syncCurrentPostToGhost();
			}
		});

		// Add command for browsing and syncing posts from Ghost
		this.addCommand({
			id: "browse-ghost-posts",
			name: "Browse and sync posts from Ghost",
			callback: () => {
				this.browseGhostPosts();
			}
		});

		// Add command for syncing specific post from Ghost by title
		this.addCommand({
			id: "sync-from-ghost-by-title",
			name: "Sync post from Ghost by title",
			callback: () => {
				this.syncFromGhostByTitle();
			}
		});

		// Add command for syncing all posts from Ghost
		this.addCommand({
			id: "sync-all-from-ghost",
			name: "Sync all posts from Ghost to local",
			callback: () => {
				this.syncAllFromGhost();
			}
		});

		// Add command for creating new post
		this.addCommand({
			id: "create-new-post",
			name: "Ghost Sync: Create new post",
			callback: () => {
				this.createNewPost();
			}
		});

		// Add command for opening sync status view
		this.addCommand({
			id: "open-sync-status",
			name: "Open Ghost Sync Status",
			callback: () => {
				this.activateSyncStatusView();
			}
		});

		// Add settings tab
		this.addSettingTab(new GhostSyncSettingTab(this.app, this));
	}

	async syncCurrentPostToGhost() {
		// Try multiple ways to get the current file to be more robust
		let file: TFile | null = null;

		// First try to get from active editor (most reliable)
		const activeEditor = this.app.workspace.activeEditor;
		if (activeEditor?.file) {
			file = activeEditor.file;
		} else {
			// Fallback to active markdown view
			const activeView = this.app.workspace.getActiveViewOfType(MarkdownView);
			if (activeView?.file) {
				file = activeView.file;
			} else {
				// Last resort: check all markdown views for the most recently active one
				const markdownLeaves = this.app.workspace.getLeavesOfType('markdown');
				for (const leaf of markdownLeaves) {
					if (leaf.view instanceof MarkdownView && leaf.view.file) {
						file = leaf.view.file;
						break;
					}
				}
			}
		}

		if (!file) {
			new Notice("No active markdown file");
			return;
		}

		// Check if the file is in the articles directory
		const articlesPath = path.normalize(this.settings.articlesDir);
		const filePath = path.normalize(file.path);

		if (!filePath.startsWith(articlesPath)) {
			new Notice(`File must be in the ${this.settings.articlesDir} directory to sync to Ghost`);
			return;
		}

		let title = '';
		let slug = '';
		let isUpdate = false;

		try {
			// Validate settings
			if (!this.settings.ghostAdminApiKey) {
				new Notice("Ghost Admin API key not configured. Please check plugin settings.");
				return;
			}

			// Read and parse the file content
			const content = await this.app.vault.read(file);
			const { frontMatter, markdownContent } = ContentConverter.parseArticle(content);

			// Normalize frontmatter using the centralized property mapping
			const normalizedFrontMatter = PropertyMapper.normalizeToGhost(frontMatter);

			if (!normalizedFrontMatter.title) {
				new Notice("Could not find title in frontmatter");
				return;
			}

			title = normalizedFrontMatter.title;
			new Notice(`Syncing "${title}" to Ghost...`);

			// Create Ghost API client
			const ghostAPI = new ObsidianGhostAPI(this.settings.ghostUrl, this.settings.ghostAdminApiKey);

			// Check if post already exists
			slug = normalizedFrontMatter.slug || ContentConverter.slugify(title);
			let existingPost = null;

			try {
				existingPost = await ghostAPI.getPostBySlug(slug);
			} catch (error: any) {
				// Handle 404 - post doesn't exist in Ghost yet, which is fine for new posts
				if (error.message?.includes('404') || error.status === 404) {
					existingPost = null;
				} else {
					// Re-throw other errors
					throw error;
				}
			}

			// Check if post already exists to determine if this is an update
			isUpdate = !!existingPost;

			// Create post data with proper update flag and existing post data
			const postData = ContentConverter.createGhostPostData(frontMatter, markdownContent, {
				status: 'published',
				isUpdate: isUpdate,
				existingPost: existingPost
			});

			let result;
			if (isUpdate) {
				result = await ghostAPI.updatePost(postData);
				new Notice(`Updated "${title}" in Ghost`);
			} else {
				result = await ghostAPI.createPost(postData);
				new Notice(`Created "${title}" in Ghost`);
			}

			if (this.settings.verbose) {
				console.log("Ghost sync result:", result);
			}

			// Update local file frontmatter with Ghost response data to ensure sync
			if (result) {
				try {
					// Read current file content
					const currentContent = await this.app.vault.read(file);
					const { frontMatter, markdownContent } = ContentConverter.parseArticle(currentContent);

					// Update frontmatter with Ghost response data using proper property mapping
					const ghostData = PropertyMapper.normalizeToObsidian({
						slug: result.slug,
						status: result.status,
						visibility: result.visibility || 'public',
						created_at: result.created_at,
						updated_at: result.updated_at,
						...(result.published_at && { published_at: result.published_at }),
						...(result.newsletter?.name && { newsletter: result.newsletter.name }),
						...(result.email && { email_sent: 'Yes' }),
						...(result.tags && { tags: result.tags.map((tag: any) => tag.name) }),
						...(result.primary_tag && { primary_tag: result.primary_tag.name }),
						...(result.feature_image && { feature_image: result.feature_image }),
						featured: result.featured || false
					});

					const updatedFrontMatter = {
						...frontMatter,
						...ghostData
					};

					// Reconstruct the file with updated frontmatter but original content
					const yamlFrontmatter = ContentConverter.objectToYaml(updatedFrontMatter);
					const updatedContent = `---\n${yamlFrontmatter}---\n\n${markdownContent}`;

					// Update the local file
					await this.app.vault.modify(file, updatedContent);

					if (this.settings.verbose) {
						console.log("Updated local file frontmatter with Ghost response data");
					}
				} catch (error) {
					console.error("Error updating local file with Ghost data:", error);
					// Don't fail the whole operation for this
				}
			}

		} catch (error) {
			console.error('=== SYNC TO GHOST ERROR ===');
			console.error('File path:', file.path);
			console.error('Post title:', title);
			console.error('Post slug:', slug);
			console.error('Is update:', isUpdate);
			console.error('Full error:', error);
			console.error('=== END SYNC TO GHOST ERROR ===');

			new Notice(`Error syncing "${title}" to Ghost: ${error.message}`);
		}
	}

	async syncFromGhostByTitle() {
		const title = await this.promptForTitle();
		if (!title)
			return;

		try {
			if (!this.settings.ghostAdminApiKey) {
				new Notice("Ghost Admin API key not configured. Please check plugin settings.");
				return;
			}

			new Notice(`Syncing "${title}" from Ghost...`);
			const ghostAPI = new ObsidianGhostAPI(this.settings.ghostUrl, this.settings.ghostAdminApiKey);
			const posts = await ghostAPI.getPosts();
			const post = posts.find((p) => p.title.toLowerCase() === title.toLowerCase());

			if (!post) {
				new Notice(`Post "${title}" not found in Ghost`);
				return;
			}

			// Convert post to article format
			const articleContent = ContentConverter.convertGhostPostToArticle(post);
			const filename = post.slug + '.md';
			const filePath = path.posix.join(this.settings.articlesDir, filename);

			// Ensure the directory exists
			const dir = path.dirname(filePath);
			if (dir !== '.' && dir !== this.settings.articlesDir) {
				await this.app.vault.createFolder(dir).catch(() => {
					// Folder might already exist, ignore error
				});
			}

			// Check if file already exists
			const existingFile = this.app.vault.getAbstractFileByPath(filePath);
			if (existingFile) {
				// Update existing file
				await this.app.vault.modify(existingFile as any, articleContent);
				new Notice(`Updated "${post.title}" in ${filePath}`);
			} else {
				// Create new file
				await this.app.vault.create(filePath, articleContent);
				new Notice(`Created "${post.title}" in ${filePath}`);
			}

			if (this.settings.verbose) {
				console.log("Ghost sync result:", { post: post.title, file: filePath });
			}

		} catch (error) {
			console.error("Error syncing from Ghost:", error);
			new Notice(`Error syncing from Ghost: ${error.message}`);
		}
	}

	async browseGhostPosts() {
		try {
			// Validate settings
			if (!this.settings.ghostAdminApiKey) {
				new Notice("Ghost Admin API key not configured. Please check plugin settings.");
				return;
			}

			new Notice("Fetching posts from Ghost...");

			// Create Ghost API client
			const ghostAPI = new ObsidianGhostAPI(this.settings.ghostUrl, this.settings.ghostAdminApiKey);

			// Get all posts
			const posts = await ghostAPI.getPosts();

			if (posts.length === 0) {
				new Notice("No posts found in Ghost");
				return;
			}

			// Show post selection modal
			const modal = new PostSelectionModal(this.app, posts, async (selectedPost) => {
				try {
					new Notice(`Syncing "${selectedPost.title}" from Ghost...`);

					// Convert post to article format
					const articleContent = ContentConverter.convertGhostPostToArticle(selectedPost);
					const filename = selectedPost.slug + '.md';
					const filePath = path.posix.join(this.settings.articlesDir, filename);

					// Ensure the directory exists
					const dir = path.dirname(filePath);
					if (dir !== '.' && dir !== this.settings.articlesDir) {
						await this.app.vault.createFolder(dir).catch(() => {
							// Folder might already exist, ignore error
						});
					}

					// Check if file already exists
					const existingFile = this.app.vault.getAbstractFileByPath(filePath);
					if (existingFile) {
						// Update existing file
						await this.app.vault.modify(existingFile as any, articleContent);
						new Notice(`Updated "${selectedPost.title}" in ${filePath}`);
					} else {
						// Create new file
						await this.app.vault.create(filePath, articleContent);
						new Notice(`Created "${selectedPost.title}" in ${filePath}`);
					}

					if (this.settings.verbose) {
						console.log("Ghost sync result:", { post: selectedPost.title, file: filePath });
					}
				} catch (error) {
					console.error("Error syncing selected post:", error);
					new Notice(`Error syncing "${selectedPost.title}": ${error.message}`);
				}
			});

			modal.open();

		} catch (error) {
			console.error("Error fetching posts from Ghost:", error);
			new Notice(`Error fetching posts from Ghost: ${error.message}`);
		}
	}

	async syncAllFromGhost() {
		try {
			// Validate settings
			if (!this.settings.ghostAdminApiKey) {
				new Notice("Ghost Admin API key not configured. Please check plugin settings.");
				return;
			}

			new Notice("Syncing all posts from Ghost...");

			// Create Ghost API client
			const ghostAPI = new ObsidianGhostAPI(this.settings.ghostUrl, this.settings.ghostAdminApiKey);

			// Get all posts
			const posts = await ghostAPI.getPosts();

			if (posts.length === 0) {
				new Notice("No posts found in Ghost");
				return;
			}

			let syncedCount = 0;
			const errors: string[] = [];

			for (const post of posts) {
				try {
					// Convert post to article format
					const articleContent = ContentConverter.convertGhostPostToArticle(post);
					const filename = post.slug + '.md';
					const filePath = path.posix.join(this.settings.articlesDir, filename);

					// Ensure the directory exists
					const dir = path.dirname(filePath);
					if (dir !== '.' && dir !== this.settings.articlesDir) {
						await this.app.vault.createFolder(dir).catch(() => {
							// Folder might already exist, ignore error
						});
					}

					// Check if file already exists
					const existingFile = this.app.vault.getAbstractFileByPath(filePath);
					if (existingFile) {
						// Update existing file
						await this.app.vault.modify(existingFile as any, articleContent);
					} else {
						// Create new file
						await this.app.vault.create(filePath, articleContent);
					}

					syncedCount++;

					if (this.settings.verbose) {
						console.log(`Synced: ${post.title} → ${filePath}`);
					}

				} catch (error) {
					const errorMsg = `Failed to sync "${post.title}": ${error.message}`;
					errors.push(errorMsg);
					console.error(errorMsg, error);
				}
			}

			if (errors.length > 0) {
				new Notice(`Synced ${syncedCount} posts with ${errors.length} errors. Check console for details.`);
				console.error("Sync errors:", errors);
			} else {
				new Notice(`Successfully synced all ${syncedCount} posts from Ghost`);
			}

			if (this.settings.verbose) {
				console.log(`Sync complete: ${syncedCount} synced, ${errors.length} errors`);
			}

		} catch (error) {
			console.error("Error syncing from Ghost:", error);
			new Notice(`Error syncing from Ghost: ${error.message}`);
		}
	}

	async promptForTitle(): Promise<string | null> {
		return new Promise((resolve) => {
			const modal = new TitleInputModal(this.app, (title) => {
				resolve(title);
			});
			modal.open();
		});
	}

	getVaultPath(): string {
		// Try to get the vault path from the adapter
		const adapter = this.app.vault.adapter as any;
		if (adapter.basePath) {
			return adapter.basePath;
		}
		// Fallback - try to use the vault name or current directory
		return ".";
	}

	async createNewPost() {
		// Prompt for title
		const title = await this.promptForTitle();
		if (!title) {
			return;
		}

		try {
			// Create slug from title
			const slug = ContentConverter.slugify(title);

			// Check if file already exists
			const filename = slug + '.md';
			const filePath = path.posix.join(this.settings.articlesDir, filename);
			const existingFile = this.app.vault.getAbstractFileByPath(filePath);

			if (existingFile) {
				new Notice(`File "${filename}" already exists in ${this.settings.articlesDir}`);
				return;
			}

			// Create timestamps
			const now = new Date();
			const createdAt = now.toISOString();

			// Create frontmatter using the centralized property mapping
			const frontmatter = PropertyMapper.normalizeToObsidian({
				title: title,
				slug: slug,
				status: 'draft',
				created_at: createdAt,
				updated_at: createdAt,
				tags: [] as string[]
			});

			// 8. Featured Image (not set for new posts)
			// Can be added later

			// 9. Featured (not set for new posts)
			// Can be added later

			// Create article content with frontmatter and empty content
			const yamlFrontmatter = ContentConverter.objectToYaml(frontmatter);
			const articleContent = `---\n${yamlFrontmatter}---\n\n# ${title}\n\nWrite your content here...\n`;

			// Ensure articles directory exists
			const articlesDir = this.settings.articlesDir;
			try {
				await this.app.vault.createFolder(articlesDir);
			} catch (error) {
				// Folder might already exist, ignore error
			}

			// Create the file
			const file = await this.app.vault.create(filePath, articleContent);

			// Open the file
			const leaf = this.app.workspace.getUnpinnedLeaf();
			await leaf.openFile(file);

			new Notice(`Created new post: "${title}"`);

			if (this.settings.verbose) {
				console.log(`Created new post: ${title} → ${filePath}`);
			}

		} catch (error) {
			console.error("Error creating new post:", error);
			new Notice(`Error creating new post: ${error.message}`);
		}
	}

	async activateSyncStatusView() {
		const { workspace } = this.app;

		let leaf: WorkspaceLeaf | null = null;
		const leaves = workspace.getLeavesOfType(VIEW_TYPE_GHOST_SYNC_STATUS);

		if (leaves.length > 0) {
			// A leaf with our view already exists, use that
			leaf = leaves[0];
		} else {
			// Our view could not be found in the workspace, create a new leaf
			// in the right sidebar for it
			leaf = workspace.getRightLeaf(false);
			await leaf.setViewState({ type: VIEW_TYPE_GHOST_SYNC_STATUS, active: true });
		}

		// "Reveal" the leaf in case it is in a collapsed sidebar
		workspace.revealLeaf(leaf);
	}

	async loadSettings() {
		this.settings = Object.assign({}, DEFAULT_SETTINGS, await this.loadData());
	}

	async saveSettings() {
		await this.saveData(this.settings);
	}

	async loadNewsletters(): Promise<void> {
		if (!this.settings.ghostAdminApiKey || this.newslettersLoaded) {
			return;
		}

		try {
			const ghostAPI = new ObsidianGhostAPI(this.settings.ghostUrl, this.settings.ghostAdminApiKey);
			this.newsletters = await ghostAPI.getNewsletters();
			this.newslettersLoaded = true;

			if (this.settings.verbose) {
				console.log('Loaded newsletters:', this.newsletters.map(n => n.name));
			}
		} catch (error) {
			console.error('Error loading newsletters:', error);
			this.newsletters = [];
			this.newslettersLoaded = false;
		}
	}

	getNewsletters(): GhostNewsletter[] {
		return this.newsletters;
	}

	getNewsletterBySlug(slug: string): GhostNewsletter | null {
		return this.newsletters.find(newsletter => newsletter.slug === slug) || null;
	}

	getNewsletterByName(name: string): GhostNewsletter | null {
		return this.newsletters.find(newsletter => newsletter.name === name) || null;
	}

	async refreshNewsletters(): Promise<void> {
		this.newslettersLoaded = false;
		await this.loadNewsletters();
	}
}

// Simple modal for title input
class TitleInputModal extends Modal {
	result: string;
	onSubmit: (result: string) => void;

	constructor(app: App, onSubmit: (result: string) => void) {
		super(app);
		this.onSubmit = onSubmit;
	}

	onOpen() {
		const { contentEl } = this;

		contentEl.createEl("h2", { text: "Enter post title" });

		const inputEl = contentEl.createEl("input", {
			type: "text",
			placeholder: "Post title..."
		});
		inputEl.focus();

		const buttonEl = contentEl.createEl("button", {
			text: "Create"
		});

		const handleSubmit = () => {
			const title = inputEl.value.trim();
			if (title) {
				this.close();
				this.onSubmit(title);
			}
		};

		buttonEl.onclick = handleSubmit;
		inputEl.onkeydown = (e) => {
			if (e.key === "Enter") {
				handleSubmit();
			}
		};
	}

	onClose() {
		const { contentEl } = this;
		contentEl.empty();
	}
}
