import { vi } from 'vitest';
import type { SyncStatusData } from '../src/components/types';

export function createMockPlugin() {
  return {
    settings: {
      articlesDir: 'articles',
      ghostUrl: 'https://test.ghost.io',
      ghostAdminApiKey: 'test-key',
      ghostContentApiKey: 'content-key',
      defaultNewsletter: 'default',
      defaultVisibility: 'public',
      defaultStatus: 'draft'
    },
    syncCurrentPostToGhost: vi.fn().mockResolvedValue(undefined),
    app: {
      workspace: {
        getActiveFile: vi.fn().mockReturnValue(null)
      },
      vault: {
        read: vi.fn().mockResolvedValue(''),
        modify: vi.fn().mockResolvedValue(undefined)
      }
    }
  };
}

export function createMockFile(path: string = 'articles/test-post.md') {
  return {
    path,
    name: path.split('/').pop() || 'test.md',
    basename: path.split('/').pop()?.replace('.md', '') || 'test',
    extension: 'md'
  };
}

export function createMockSyncStatus(overrides: Partial<SyncStatusData> = {}): SyncStatusData {
  return {
    title: 'unknown',
    slug: 'unknown',
    status: 'unknown',
    tags: 'unknown',
    featured: 'unknown',
    feature_image: 'unknown',
    visibility: 'unknown',
    primary_tag: 'unknown',
    created_at: 'unknown',
    updated_at: 'unknown',
    published_at: 'unknown',
    synced_at: 'unknown',
    newsletter: 'unknown',
    email_sent: 'unknown',
    ...overrides
  };
}

export function createMockGhostPost() {
  return {
    id: 'test-id',
    title: 'Test Post',
    slug: 'test-post',
    status: 'draft',
    visibility: 'public',
    created_at: '2023-01-01T00:00:00.000Z',
    updated_at: '2023-01-01T00:00:00.000Z',
    published_at: null,
    tags: [
      { name: 'test', slug: 'test' },
      { name: 'vitest', slug: 'vitest' }
    ],
    primary_tag: { name: 'test', slug: 'test' },
    feature_image: null,
    featured: false,
    html: '<p>Test content</p>',
    plaintext: 'Test content',
    excerpt: 'Test content',
    url: 'https://test.ghost.io/test-post/',
    uuid: 'test-uuid',
    comment_id: 'test-comment-id'
  };
}

export function createSyncedSyncStatus(): SyncStatusData {
  return {
    title: 'synced',
    slug: 'synced',
    status: 'synced',
    tags: 'synced',
    featured: 'synced',
    feature_image: 'synced',
    visibility: 'synced',
    primary_tag: 'synced',
    created_at: 'synced',
    updated_at: 'synced',
    published_at: 'synced',
    synced_at: 'synced',
    newsletter: 'synced',
    email_sent: 'synced'
  };
}

export function createDifferentSyncStatus(): SyncStatusData {
  return {
    title: 'different',
    slug: 'different',
    status: 'different',
    tags: 'different',
    featured: 'different',
    feature_image: 'different',
    visibility: 'different',
    primary_tag: 'different',
    created_at: 'different',
    updated_at: 'different',
    published_at: 'different',
    synced_at: 'different',
    newsletter: 'different',
    email_sent: 'different'
  };
}

export function waitForNextTick() {
  return new Promise(resolve => setTimeout(resolve, 0));
}

export function createMockContainer() {
  const container = document.createElement('div');
  document.body.appendChild(container);
  return container;
}

export function cleanupContainer(container: HTMLElement) {
  if (container.parentNode) {
    container.parentNode.removeChild(container);
  }
}
