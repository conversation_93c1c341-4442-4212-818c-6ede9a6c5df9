import { describe, it, expect, beforeEach, vi } from 'vitest';
import { SmartSyncService } from '../../src/services/smart-sync-service';
import { SyncDecision } from '../../src/types';
import type { TFile } from 'obsidian';
import type { GhostPost, LocalPost } from '../../src/types';

describe('SmartSyncService', () => {
  let service: SmartSyncService;
  let mockGhostAPI: any;
  let mockReadFile: any;
  let mockWriteFile: any;
  let mockParseMarkdown: any;
  let mockFile: TFile;

  beforeEach(() => {
    mockGhostAPI = {
      getPostBySlug: vi.fn(),
      createPost: vi.fn(),
      updatePost: vi.fn()
    };

    mockReadFile = vi.fn();
    mockWriteFile = vi.fn();
    mockParseMarkdown = vi.fn();

    service = new SmartSyncService({
      ghostAPI: mockGhostAPI,
      readFile: mockReadFile,
      writeFile: mockWriteFile,
      parseMarkdown: mockParseMarkdown
    });

    mockFile = {
      path: 'articles/test-post.md',
      stat: {
        mtime: Date.now(),
        ctime: Date.now(),
        size: 1000
      }
    } as TFile;
  });

  describe('parseLocalPost', () => {
    it('should parse local post with sync timestamp', async () => {
      const fileContent = `---
title: Test Post
slug: test-post
synced_at: 2024-01-01T10:00:00.000Z
---

Test content here`;

      const parsedContent = {
        frontMatter: {
          title: 'Test Post',
          slug: 'test-post',
          synced_at: '2024-01-01T10:00:00.000Z'
        },
        content: 'Test content here'
      };

      mockReadFile.mockResolvedValue(fileContent);
      mockParseMarkdown.mockReturnValue(parsedContent);

      const result = await service.parseLocalPost(mockFile);

      expect(result).toEqual({
        frontMatter: parsedContent.frontMatter,
        content: parsedContent.content,
        syncedAt: '2024-01-01T10:00:00.000Z',
        fileModifiedAt: mockFile.stat!.mtime,
        filePath: mockFile.path
      });
    });

    it('should handle missing sync timestamp', async () => {
      const fileContent = `---
title: Test Post
slug: test-post
---

Test content here`;

      const parsedContent = {
        frontMatter: {
          title: 'Test Post',
          slug: 'test-post'
        },
        content: 'Test content here'
      };

      mockReadFile.mockResolvedValue(fileContent);
      mockParseMarkdown.mockReturnValue(parsedContent);

      const result = await service.parseLocalPost(mockFile);

      expect(result.syncedAt).toBeUndefined();
    });
  });

  describe('analyzeSyncNeeded', () => {
    let localPost: LocalPost;

    beforeEach(() => {
      localPost = {
        frontMatter: {
          title: 'Test Post',
          slug: 'test-post',
          synced_at: '2024-01-01T10:00:00.000Z'
        },
        content: 'Test content',
        syncedAt: '2024-01-01T10:00:00.000Z',
        fileModifiedAt: new Date('2024-01-01T10:30:00.000Z').getTime(),
        filePath: 'test.md'
      };
    });

    it('should sync to Ghost when post does not exist', async () => {
      const analysis = await service.analyzeSyncNeeded(localPost);

      expect(analysis.decision).toBe(SyncDecision.SYNC_TO_GHOST);
      expect(analysis.reason).toBe("Post doesn't exist in Ghost yet");
    });

    it('should sync from Ghost when Ghost is newer', async () => {
      const ghostPost: GhostPost = {
        id: '1',
        title: 'Test Post',
        slug: 'test-post',
        status: 'draft',
        featured: false,
        created_at: '2024-01-01T09:00:00.000Z',
        updated_at: '2024-01-01T11:00:00.000Z' // Newer than sync time
      };

      // File was not modified since sync time (no local changes)
      localPost.fileModifiedAt = new Date('2024-01-01T09:30:00.000Z').getTime();

      const analysis = await service.analyzeSyncNeeded(localPost, ghostPost);

      expect(analysis.decision).toBe(SyncDecision.SYNC_FROM_GHOST);
      expect(analysis.reason).toBe('Ghost has newer changes');
    });

    it('should sync to Ghost when local file is newer', async () => {
      const ghostPost: GhostPost = {
        id: '1',
        title: 'Test Post',
        slug: 'test-post',
        status: 'draft',
        featured: false,
        created_at: '2024-01-01T09:00:00.000Z',
        updated_at: '2024-01-01T09:30:00.000Z' // Older than sync time
      };

      // Content was changed after sync time
      localPost.changedAt = '2024-01-01T10:30:00.000Z';

      const analysis = await service.analyzeSyncNeeded(localPost, ghostPost);

      expect(analysis.decision).toBe(SyncDecision.SYNC_TO_GHOST);
      expect(analysis.reason).toBe('Local content has newer changes');
    });

    it('should detect conflict when both sides have changes', async () => {
      const ghostPost: GhostPost = {
        id: '1',
        title: 'Test Post',
        slug: 'test-post',
        status: 'draft',
        featured: false,
        created_at: '2024-01-01T09:00:00.000Z',
        updated_at: '2024-01-01T11:00:00.000Z' // Newer than sync time
      };

      // Content was also changed after sync time
      localPost.changedAt = '2024-01-01T10:30:00.000Z';

      const analysis = await service.analyzeSyncNeeded(localPost, ghostPost);

      expect(analysis.decision).toBe(SyncDecision.CONFLICT);
      expect(analysis.reason).toBe('Both Ghost and local content have changes since last sync');
    });

    it('should indicate no sync needed when nothing changed', async () => {
      const ghostPost: GhostPost = {
        id: '1',
        title: 'Test Post',
        slug: 'test-post',
        status: 'draft',
        featured: false,
        created_at: '2024-01-01T09:00:00.000Z',
        updated_at: '2024-01-01T09:30:00.000Z' // Older than sync time
      };

      // File was not modified after sync time
      localPost.fileModifiedAt = new Date('2024-01-01T09:45:00.000Z').getTime();

      const analysis = await service.analyzeSyncNeeded(localPost, ghostPost);

      expect(analysis.decision).toBe(SyncDecision.NO_SYNC_NEEDED);
      expect(analysis.reason).toBe('No changes detected since last sync');
    });

    it('should handle first sync when local is newer', async () => {
      localPost.syncedAt = undefined;
      localPost.changedAt = '2024-01-01T12:00:00.000Z';

      const ghostPost: GhostPost = {
        id: '1',
        title: 'Test Post',
        slug: 'test-post',
        status: 'draft',
        featured: false,
        created_at: '2024-01-01T09:00:00.000Z',
        updated_at: '2024-01-01T11:00:00.000Z'
      };

      const analysis = await service.analyzeSyncNeeded(localPost, ghostPost);

      expect(analysis.decision).toBe(SyncDecision.SYNC_TO_GHOST);
      expect(analysis.reason).toBe('First sync - local content is newer');
    });

    it('should handle first sync when Ghost is newer', async () => {
      localPost.syncedAt = undefined;
      localPost.fileModifiedAt = new Date('2024-01-01T10:00:00.000Z').getTime();

      const ghostPost: GhostPost = {
        id: '1',
        title: 'Test Post',
        slug: 'test-post',
        status: 'draft',
        featured: false,
        created_at: '2024-01-01T09:00:00.000Z',
        updated_at: '2024-01-01T11:00:00.000Z'
      };

      const analysis = await service.analyzeSyncNeeded(localPost, ghostPost);

      expect(analysis.decision).toBe(SyncDecision.SYNC_FROM_GHOST);
      expect(analysis.reason).toBe('First sync - Ghost post is newer');
    });
  });

  describe('syncToGhost', () => {
    it('should create new post when it does not exist', async () => {
      const localPost: LocalPost = {
        frontMatter: { title: 'Test Post', slug: 'test-post' },
        content: 'Test content',
        fileModifiedAt: Date.now(),
        filePath: 'test.md'
      };

      const createdPost: GhostPost = {
        id: '1',
        title: 'Test Post',
        slug: 'test-post',
        status: 'draft',
        featured: false,
        created_at: '2024-01-01T10:00:00.000Z',
        updated_at: '2024-01-01T10:00:00.000Z'
      };

      mockGhostAPI.getPostBySlug.mockResolvedValue(null);
      mockGhostAPI.createPost.mockResolvedValue(createdPost);

      const result = await service.syncToGhost(localPost);

      expect(mockGhostAPI.createPost).toHaveBeenCalled();
      expect(result).toBe(createdPost);
      expect(localPost.syncedAt).toBe(createdPost.updated_at);
    });

    it('should update existing post', async () => {
      const localPost: LocalPost = {
        frontMatter: { title: 'Test Post', slug: 'test-post' },
        content: 'Test content',
        fileModifiedAt: Date.now(),
        filePath: 'test.md'
      };

      const existingPost: GhostPost = {
        id: '1',
        title: 'Old Title',
        slug: 'test-post',
        status: 'draft',
        featured: false,
        created_at: '2024-01-01T09:00:00.000Z',
        updated_at: '2024-01-01T09:00:00.000Z'
      };

      const updatedPost: GhostPost = {
        ...existingPost,
        title: 'Test Post',
        updated_at: '2024-01-01T10:00:00.000Z'
      };

      mockGhostAPI.getPostBySlug.mockResolvedValue(existingPost);
      mockGhostAPI.updatePost.mockResolvedValue(updatedPost);

      const result = await service.syncToGhost(localPost);

      expect(mockGhostAPI.updatePost).toHaveBeenCalled();
      expect(result).toBe(updatedPost);
      expect(localPost.syncedAt).toBe(updatedPost.updated_at);
    });

    it('should throw error when post has no slug', async () => {
      const localPost: LocalPost = {
        frontMatter: { title: 'Test Post' }, // No slug
        content: 'Test content',
        fileModifiedAt: Date.now(),
        filePath: 'test.md'
      };

      await expect(service.syncToGhost(localPost)).rejects.toThrow('Post must have a slug to sync to Ghost');
    });
  });

  describe('syncFromGhost', () => {
    it('should update local file with Ghost content', async () => {
      const ghostPost: GhostPost = {
        id: '1',
        title: 'Test Post',
        slug: 'test-post',
        status: 'draft',
        featured: false,
        created_at: '2024-01-01T10:00:00.000Z',
        updated_at: '2024-01-01T10:00:00.000Z',
        html: '<p>Test content</p>'
      };

      await service.syncFromGhost(mockFile, ghostPost);

      expect(mockWriteFile).toHaveBeenCalledWith(mockFile, expect.stringContaining('Test Post'));
    });
  });
});
