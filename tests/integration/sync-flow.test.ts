import { describe, it, expect, beforeEach, vi } from 'vitest';
import { SmartSyncService } from '../../src/services/smart-sync-service';
import { ContentConverter } from '../../src/utils/content-converter';
import { SyncDecision } from '../../src/types';
import type { TFile } from 'obsidian';

describe('Sync Flow Integration Tests', () => {
  let service: SmartSyncService;
  let mockGhostAPI: any;
  let mockFiles: Map<string, string>;
  let mockFile: TFile;

  beforeEach(() => {
    mockFiles = new Map();

    mockGhostAPI = {
      getPostBySlug: vi.fn(),
      createPost: vi.fn(),
      updatePost: vi.fn()
    };

    const mockReadFile = vi.fn().mockImplementation((file: TFile) => {
      const content = mockFiles.get(file.path);
      if (!content) throw new Error(`File not found: ${file.path}`);
      return Promise.resolve(content);
    });

    const mockWriteFile = vi.fn().mockImplementation((file: TFile, content: string) => {
      mockFiles.set(file.path, content);
      return Promise.resolve();
    });

    service = new SmartSyncService({
      ghostAPI: mockGhostAPI,
      readFile: mockReadFile,
      writeFile: mockWriteFile,
      parseMarkdown: (content: string) => {
        const parsed = ContentConverter.parseMarkdown(content);
        return { frontMatter: parsed.frontMatter, content: parsed.markdownContent };
      }
    });

    mockFile = {
      path: 'articles/test-post.md',
      stat: {
        mtime: new Date('2024-01-01T12:00:00.000Z').getTime(),
        ctime: new Date('2024-01-01T10:00:00.000Z').getTime(),
        size: 1000
      }
    } as TFile;
  });

  describe('First-time sync scenarios', () => {
    it('should sync new local post to Ghost', async () => {
      // Setup: Local file with no sync timestamp
      const localContent = `---
title: My New Post
slug: my-new-post
status: draft
---

# My New Post

This is a brand new post created locally.`;

      mockFiles.set(mockFile.path, localContent);

      // Ghost API returns null (post doesn't exist)
      mockGhostAPI.getPostBySlug.mockResolvedValue(null);
      mockGhostAPI.createPost.mockResolvedValue({
        id: '1',
        title: 'My New Post',
        slug: 'my-new-post',
        status: 'draft' as const,
        featured: false,
        created_at: '2024-01-01T12:00:00.000Z',
        updated_at: '2024-01-01T12:00:00.000Z'
      });

      // Parse local post
      const localPost = await service.parseLocalPost(mockFile);
      expect(localPost.syncedAt).toBeUndefined();

      // Analyze sync decision
      const analysis = await service.analyzeSyncNeeded(localPost);
      expect(analysis.decision).toBe(SyncDecision.SYNC_TO_GHOST);

      // Execute sync
      const result = await service.syncToGhost(localPost);

      expect(mockGhostAPI.createPost).toHaveBeenCalledWith(
        expect.objectContaining({
          title: 'My New Post',
          slug: 'my-new-post',
          html: expect.stringContaining('My New Post')
        })
      );
      expect(result.id).toBe('1');
      expect(localPost.syncedAt).toBe('2024-01-01T12:00:00.000Z');
    });

    it('should sync new Ghost post to local', async () => {
      // Setup: Ghost post exists but no local file
      const ghostPost = {
        id: '1',
        title: 'Ghost Post',
        slug: 'ghost-post',
        status: 'draft' as const,
        featured: false,
        created_at: '2024-01-01T10:00:00.000Z',
        updated_at: '2024-01-01T11:00:00.000Z',
        html: '<h1>Ghost Post</h1><p>This post was created in Ghost.</p>',
        tags: []
      };

      // Create empty local file (simulating new file creation)
      const emptyContent = `---
title: ""
slug: ghost-post
---

`;
      mockFiles.set(mockFile.path, emptyContent);

      // File modification time is older than Ghost
      mockFile.stat!.mtime = new Date('2024-01-01T09:00:00.000Z').getTime();

      const localPost = await service.parseLocalPost(mockFile);
      const analysis = await service.analyzeSyncNeeded(localPost, ghostPost);

      expect(analysis.decision).toBe(SyncDecision.SYNC_FROM_GHOST);

      // Execute sync from Ghost
      await service.syncFromGhost(mockFile, ghostPost);

      const updatedContent = mockFiles.get(mockFile.path);
      expect(updatedContent).toContain('Title: "Ghost Post"');
      expect(updatedContent).toContain('Slug: "ghost-post"');
      expect(updatedContent).toContain('Synced At:');
      expect(updatedContent).toContain('# Ghost Post');
    });
  });

  describe('Bidirectional sync scenarios', () => {
    it('should sync local changes to Ghost when local is newer', async () => {
      // Setup: Both local and Ghost exist, local has newer changes
      const localContent = `---
title: Updated Title
slug: test-post
synced_at: 2024-01-01T10:00:00.000Z
changed_at: 2024-01-01T11:00:00.000Z
---

# Updated Title

This content was updated locally.`;

      mockFiles.set(mockFile.path, localContent);

      // File was modified after last sync
      mockFile.stat!.mtime = new Date('2024-01-01T11:00:00.000Z').getTime();

      const ghostPost = {
        id: '1',
        title: 'Old Title',
        slug: 'test-post',
        status: 'draft' as const,
        featured: false,
        created_at: '2024-01-01T09:00:00.000Z',
        updated_at: '2024-01-01T09:30:00.000Z', // Older than sync time
        html: '<h1>Old Title</h1><p>Old content</p>'
      };

      mockGhostAPI.getPostBySlug.mockResolvedValue(ghostPost);
      mockGhostAPI.updatePost.mockResolvedValue({
        ...ghostPost,
        title: 'Updated Title',
        updated_at: '2024-01-01T11:30:00.000Z'
      });

      const localPost = await service.parseLocalPost(mockFile);
      const analysis = await service.analyzeSyncNeeded(localPost, ghostPost);

      expect(analysis.decision).toBe(SyncDecision.SYNC_TO_GHOST);

      const result = await service.syncToGhost(localPost);

      expect(mockGhostAPI.updatePost).toHaveBeenCalledWith(
        expect.objectContaining({
          id: '1',
          title: 'Updated Title',
          html: expect.stringContaining('Updated Title')
        })
      );
    });

    it('should sync Ghost changes to local when Ghost is newer', async () => {
      // Setup: Both exist, Ghost has newer changes
      const localContent = `---
title: Old Local Title
slug: test-post
synced_at: 2024-01-01T10:00:00.000Z
---

# Old Local Title

Old local content.`;

      mockFiles.set(mockFile.path, localContent);

      // File was not modified since sync
      mockFile.stat!.mtime = new Date('2024-01-01T09:30:00.000Z').getTime();

      const ghostPost = {
        id: '1',
        title: 'Updated Ghost Title',
        slug: 'test-post',
        status: 'draft' as const,
        featured: false,
        created_at: '2024-01-01T09:00:00.000Z',
        updated_at: '2024-01-01T11:00:00.000Z', // Newer than sync time
        html: '<h1>Updated Ghost Title</h1><p>Updated Ghost content</p>',
        tags: []
      };

      const localPost = await service.parseLocalPost(mockFile);
      const analysis = await service.analyzeSyncNeeded(localPost, ghostPost);

      expect(analysis.decision).toBe(SyncDecision.SYNC_FROM_GHOST);

      await service.syncFromGhost(mockFile, ghostPost);

      const updatedContent = mockFiles.get(mockFile.path);
      expect(updatedContent).toContain('Title: "Updated Ghost Title"');
      expect(updatedContent).toContain('# Updated Ghost Title');
      expect(updatedContent).toContain('Updated Ghost content');
    });

    it('should detect conflict when both sides have changes', async () => {
      // Setup: Both have changes since last sync
      const localContent = `---
title: Local Changes
slug: test-post
synced_at: 2024-01-01T10:00:00.000Z
changed_at: 2024-01-01T11:00:00.000Z
---

# Local Changes

Content updated locally.`;

      mockFiles.set(mockFile.path, localContent);

      // File was modified after sync
      mockFile.stat!.mtime = new Date('2024-01-01T11:00:00.000Z').getTime();

      const ghostPost = {
        id: '1',
        title: 'Ghost Changes',
        slug: 'test-post',
        status: 'draft' as const,
        featured: false,
        created_at: '2024-01-01T09:00:00.000Z',
        updated_at: '2024-01-01T10:30:00.000Z', // Also newer than sync time
        html: '<h1>Ghost Changes</h1><p>Content updated in Ghost</p>'
      };

      const localPost = await service.parseLocalPost(mockFile);
      const analysis = await service.analyzeSyncNeeded(localPost, ghostPost);

      expect(analysis.decision).toBe(SyncDecision.CONFLICT);
      expect(analysis.reason).toBe('Both Ghost and local content have changes since last sync');
    });

    it('should indicate no sync needed when nothing changed', async () => {
      // Setup: No changes since last sync
      const localContent = `---
title: Unchanged Post
slug: test-post
synced_at: 2024-01-01T10:00:00.000Z
---

# Unchanged Post

No changes here.`;

      mockFiles.set(mockFile.path, localContent);

      // File was not modified since sync
      mockFile.stat!.mtime = new Date('2024-01-01T09:30:00.000Z').getTime();

      const ghostPost = {
        id: '1',
        title: 'Unchanged Post',
        slug: 'test-post',
        status: 'draft' as const,
        featured: false,
        created_at: '2024-01-01T09:00:00.000Z',
        updated_at: '2024-01-01T09:45:00.000Z', // Also older than sync time
        html: '<h1>Unchanged Post</h1><p>No changes here</p>'
      };

      const localPost = await service.parseLocalPost(mockFile);
      const analysis = await service.analyzeSyncNeeded(localPost, ghostPost);

      expect(analysis.decision).toBe(SyncDecision.NO_SYNC_NEEDED);
    });
  });

  describe('Content integrity tests', () => {
    it('should never set "Content is being updated" placeholder', async () => {
      const testCases = [
        { content: '', description: 'empty content' },
        { content: '   \n  \t  ', description: 'whitespace-only content' },
        { content: 'Real content', description: 'actual content' }
      ];

      for (const testCase of testCases) {
        const localContent = `---
title: Test Post
slug: test-post
---

${testCase.content}`;

        mockFiles.set(mockFile.path, localContent);

        mockGhostAPI.getPostBySlug.mockResolvedValue(null);
        mockGhostAPI.createPost.mockResolvedValue({
          id: '1',
          title: 'Test Post',
          slug: 'test-post',
          status: 'draft' as const,
          featured: false,
          created_at: '2024-01-01T12:00:00.000Z',
          updated_at: '2024-01-01T12:00:00.000Z'
        });

        const localPost = await service.parseLocalPost(mockFile);
        await service.syncToGhost(localPost);

        const createCall = mockGhostAPI.createPost.mock.calls[mockGhostAPI.createPost.mock.calls.length - 1];
        const postData = createCall[0];

        expect(postData.html).not.toContain('Content is being updated');

        if (testCase.content.trim() === '') {
          expect(postData.html).toBe('<p></p>');
        } else {
          expect(postData.html).toContain(testCase.content);
        }
      }
    });
  });
});
