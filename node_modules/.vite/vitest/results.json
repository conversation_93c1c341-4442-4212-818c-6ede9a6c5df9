{"version": "2.1.9", "results": [[":tests/content-conversion.test.ts", {"duration": 26.160000000000082, "failed": false}], [":tests/sync/timestamp-sync.test.ts", {"duration": 6.024708000000032, "failed": false}], [":tests/utils/property-mapping.test.ts", {"duration": 5.503541000000041, "failed": false}], [":tests/integration/sync-flow.test.ts", {"duration": 27.389749999999935, "failed": false}], [":tests/components/GhostSyncView.vitest.test.ts", {"duration": 99.43658400000004, "failed": false}], [":tests/services/smart-sync-service.test.ts", {"duration": 21.998749999999973, "failed": false}], [":tests/sync/sync-status.test.ts", {"duration": 3.65887500000008, "failed": false}], [":tests/integration/no-placeholder-content.test.ts", {"duration": 10.177791999999954, "failed": false}], [":tests/utils/content-converter.test.ts", {"duration": 11.594959000000017, "failed": false}], [":tests/api/ghost-api.test.ts", {"duration": 8.670083999999974, "failed": false}], [":tests/views/sync-status-view.test.ts", {"duration": 10.074416000000042, "failed": false}], [":tests/components/StatusBadge.test.ts", {"duration": 22.692916999999966, "failed": false}], [":tests/services/obsidian-app-adapter.test.ts", {"duration": 27.400833000000034, "failed": false}], [":tests/components/component-basic.test.ts", {"duration": 2.6828749999999673, "failed": false}], [":tests/components/PropertyDisplay.test.ts", {"duration": 20.25783299999995, "failed": false}], [":tests/basic.test.ts", {"duration": 2.9650419999999826, "failed": false}]]}